package com.trinasolar.integration.service;

import com.trinasolar.integration.api.entity.InteAppSystemChange;
import com.trinasolar.integration.service.impl.DataShareServiceImpl;
import com.trinasolar.tasc.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 数据共享服务测试类
 * 用于排查 businessData 为空的问题
 */
@Slf4j
@SpringBootTest
public class DataShareServiceTest {

    @Test
    public void testBusinessDataProcessing() {
        try {
            // 创建测试数据
            InteAppSystemChange change = new InteAppSystemChange();
            change.setSimpleEnName("TEST-APP");
            
            // 模拟数据库中的 current_data 字段内容（驼峰格式，这是实际的数据格式）
            String testCurrentData = "{\n" +
                "  \"id\": 123,\n" +
                "  \"cnName\": \"人力资源门户\",\n" +
                "  \"cnSimpleName\": \"人力资源\",\n" +
                "  \"enName\": \"HR portal\",\n" +
                "  \"enSimpleName\": \"HRP\",\n" +
                "  \"code\": \"MHR-ALL-HRP\",\n" +
                "  \"appDomain\": \"mhr\",\n" +
                "  \"appName\": \"osesa\",\n" +
                "  \"productLine\": \"efap\",\n" +
                "  \"bizUnit\": \"gen\",\n" +
                "  \"bizScope\": \"group\",\n" +
                "  \"bizScopeType\": \"other\",\n" +
                "  \"description\": \"HR人力资源共享门户面向SAL/DL/IDL的自助服务平台/通过数字化服务/提升用户体验\",\n" +
                "  \"stageStatus\": \"Online\",\n" +
                "  \"actuallyTime\": \"2025-1-16\",\n" +
                "  \"version\": \"HZERO1.10\",\n" +
                "  \"businessDomain\": \"mhr\",\n" +
                "  \"accessUrl\": \"\",\n" +
                "  \"manageUrl\": \"\",\n" +
                "  \"manualUrl\": \"\"\n" +
                "}";
            
            change.setCurrentData(testCurrentData);
            
            log.info("测试数据: {}", testCurrentData);
            
            // 验证 JSON 解析
            if (JsonUtils.isJson(testCurrentData)) {
                Object parsedData = JsonUtils.parseTree(testCurrentData);
                log.info("JSON解析结果: {}", parsedData);
                
                if (parsedData instanceof Map) {
                    Map<String, Object> dataMap = (Map<String, Object>) parsedData;
                    log.info("解析后的Map keys: {}", dataMap.keySet());
                    
                    // 检查每个字段
                    dataMap.forEach((key, value) -> {
                        log.info("字段: {} = {}", key, value);
                    });
                }
            } else {
                log.error("测试数据不是有效的JSON格式");
            }
            
            // 测试模板字段
            String template = "{\"flow_domain\":\"7.0\",\"app_domain\":\"mhr\",\"app_group\":\"osesat\",\"app_name\":\"osesa\",\"code\":\"MHR-ALL-HRP\",\"cn_name\":\"人力资源门户\",\"cn_simple_name\":\"人力资源\",\"product_line\":\"efap\",\"en_name\":\"HR portal\",\"en_simple_name\":\"HRP\",\"biz_unit\":\"gen\",\"biz_scope\":\"group\",\"description\":\"HR人力资源共享门户面向SAL/DL/IDL的自助服务平台/通过数字化服务/提升用户体验\",\"stage_status\":\"Online\",\"actually_time\":\"2025-1-16\",\"remark\":\"\",\"bus_user_name\":\"倪庆英\",\"requester_contact\":\"王宏智\",\"digital_owner\":\"薛波\",\"dev_user_name\":\"于庆伟\",\"om_user_name\":\"薛波\",\"external_product_name\":\"布谷\",\"construction_type\":\"Procurement\",\"version\":\"HZERO1.10\",\"product_vendor\":\"汉得\",\"impl_vendor\":\"汉得\",\"license_mode\":\"auth_perm\",\"app_service_level\":2,\"service_hours\":\"5*8\",\"biz_scope_type\":\"other\",\"dev_language\":\"lang_java\",\"business_domain\":\"mhr\",\"app_sys_type\":2,\"bus_user_id\":\"014438\",\"sys_user_id\":\"\",\"dev_user_id\":\"137863\",\"om_user_id\":\"282605\",\"requester_contact_id\":\"314958\",\"digital_owner_id\":\"282605\",\"admin\":\"\",\"admin_name\":\"\",\"sys_user_name\":\"\",\"access_url\":\"\",\"manage_url\":\"\",\"manual_url\":\"\",\"license_desc\":\"\",\"data_version\":\"\"}";
            
            Map<String, Object> templateMap = JsonUtils.parseObject(template, Map.class);
            log.info("模板字段: {}", templateMap.keySet());
            
            // 检查字段匹配情况
            if (JsonUtils.isJson(testCurrentData)) {
                Map<String, Object> testDataMap = JsonUtils.parseObject(testCurrentData, Map.class);
                
                log.info("=== 字段匹配分析 ===");
                for (String templateField : templateMap.keySet()) {
                    boolean directMatch = testDataMap.containsKey(templateField);
                    String camelCase = toCamelCase(templateField);
                    boolean camelMatch = testDataMap.containsKey(camelCase);
                    
                    log.info("模板字段: {} | 直接匹配: {} | 驼峰匹配: {} ({})", 
                        templateField, directMatch, camelMatch, camelCase);
                }
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
    
    private String toCamelCase(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;
        
        for (char c : str.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(c);
                }
            }
        }
        
        return result.toString();
    }
}
