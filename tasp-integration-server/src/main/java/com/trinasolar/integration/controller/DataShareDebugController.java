package com.trinasolar.integration.controller;

import com.trinasolar.integration.service.impl.DataShareServiceImpl;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据共享调试控制器
 * 用于排查 businessData 为空的问题
 */
@Slf4j
@RestController
@RequestMapping("/debug/datashare")
@Tag(name = "数据共享调试")
public class DataShareDebugController {

    @Autowired
    private DataShareServiceImpl dataShareService;

    @GetMapping("/field-matching")
    @Operation(summary = "调试字段匹配逻辑")
    public CommonResult<String> debugFieldMatching() {
        try {
            dataShareService.debugFieldMatching();
            return CommonResult.success("调试完成，请查看日志");
        } catch (Exception e) {
            log.error("调试失败", e);
            return CommonResult.error("调试失败: " + e.getMessage());
        }
    }
}
